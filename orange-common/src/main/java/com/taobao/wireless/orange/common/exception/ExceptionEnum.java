package com.taobao.wireless.orange.common.exception;

/**
 * <AUTHOR>
 */

public enum ExceptionEnum {

    // A类 - 入参错误
    // 通用入参错误 (68)
    PARAM_INVALID("A68001", "入参不合法"),
    VIEW_TYPE_NOT_SUPPORT("A68002", "展示类型不支持"),
    NO_PERMISSION("A68003", "无权限"),

    // 命名空间入参错误 (01)
    NAMESPACE_NOT_EXIST("A01001", "命名空间不存在"),
    NAMESPACE_NAME_DUPLICATE("A01002", "该命名空间名字已存在"),

    // 参数入参错误 (02)
    PARAMETER_NOT_FOUND("A02001", "参数不存在"),
    PARAMETER_NOT_ONLINE("A02002", "参数未上线"),
    PARAMETER_KEY_DUPLICATE("A02003", "该参数 KEY 已存在"),
    PARAMETER_PREVIOUS_RELEASE_VERSION_NOT_MATCH("A02004", "参数[key={}]已经有新的版本"),
    PARAMETER_CONDITION_PREVIOUS_RELEASE_VERSION_NOT_MATCH("A02005", "参数[key={}]条件[name={}]已经有新的版本"),
    PARAMETER_IS_PUBLISHING("A02006", "参数正在发布中"),

    // 条件入参错误 (03)
    CONDITION_IS_PUBLISHING("A03001", "条件正在发布中"),
    CONDITION_NOT_FOUND("A03002", "条件不存在"),
    CONDITION_PREVIOUS_RELEASE_VERSION_NOT_MATCH("A03003", "条件[name={}]已经有新的版本"),
    CONDITION_NAME_DUPLICATE("A03004", "条件名字[{}]已存在"),

    // 发布单入参错误 (04)
    RELEASE_ORDER_NOT_EXIST("A04001", "发布单不存在"),
    RELEASE_ORDER_STATUS_NOT_VERIFY_PASS("A04002", "发布单未验证通过"),
    RELEASE_ORDER_STATUS_INVALID("A04003", "发布单状态不正确"),
    CREATE_NAMESPACE_FAIL("B10101", "创建命名空间失败"),
    NAMESPACE_NOT_FOUND("B10100", "命名空间不存在"),
    NAMESPACE_VERSION_CONTENT_NOT_EXIST("B10103", "命名空间版本快照不存在"),

    CREATE_RELEASE_ORDER_FAIL("B10104", "创建发布单失败"),
    CREATE_CONDITION_FAIL("B10103", "创建条件失败"),
    CREATE_PARAMETER_FAIL("B10103", "创建参数失败"),
    OSS_READ_ERROR("B10105", "读取 OSS 数据失败"),
    RESOURCE_DESERIALIZE_ERROR("B10106", "反序列化失败"),
    DESERIALIZE_EXCEPTION("B68105", "反序列化异常"),
    UPDATE_NAMESPACE_FAIL("B10102", "更新命名空间失败"),
    SYSTEM_EXCEPTION("B68101", "Orange 服务系统异常"),
    AMDP_ERROR("C81005", "调用 Amdp 服务异常"),
    MTL_ERROR("C81006", "调用 MTL 服务异常"),
    MTL_INIT_ERROR("C81007", "MTL 客户端初始化失败"),
    MTL_ENDPOINT_INVALID("C81008", "MTL endpoint 配置无效"),
    MTL_ACCESS_KEY_BLANK("C81009", "MTL accessKey 不能为空"),
    MTL_ACCESS_SECRET_BLANK("C81010", "MTL accessSecret 不能为空"),
    MTL_SEARCH_PARAMS_INVALID("C81011", "MTL 搜索参数无效，appKey 和 keyword 均不能为空"),
    MTL_HTTP_ERROR("C81012", "调用 MTL 接口失败，状态码：{}"),
    MTL_BUSINESS_ERROR("C81013", "调用 MTL 接口失败：{}"),
    MTL_PARSE_ERROR("C81014", "解析 MTL 返回结果失败"),
    BEAN_COPY_EXCEPTION("B81005", "BEAN 拷贝异常"),

    // WMCC 相关异常
    WMCC_SERVICE_NOT_AVAILABLE("C81015", "WMCC 配置发布服务不可用"),
    WMCC_PUBLISH_ERROR("C81016", "WMCC 配置发布任务提交失败"),
    WMCC_ROLLBACK_ERROR("C81017", "WMCC 配置回滚任务提交失败"),
    WMCC_QUERY_RUNNING_TASK_ERROR("C81018", "WMCC 查询正在发布的任务信息失败"),
    WMCC_QUERY_TASK_STATUS_ERROR("C81019", "WMCC 查询任务发布状态失败"),
    WMCC_GET_ROLLBACK_CONFIG_ERROR("C81020", "WMCC 获取回滚配置内容失败"),
    WMCC_PAUSE_TASK_ERROR("C81021", "WMCC 暂停任务失败"),
    WMCC_CANCEL_TASK_ERROR("C81022", "WMCC 取消任务失败");


    private String code;
    private String message;

    private ExceptionEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    // 用于生成错误码列表
//    public static void main(String[] args) {
//        System.out.println("| 错误码 | 错误详情 | \n|-----|------|");
//        for (ExceptionEnum exceptionEnum : ExceptionEnum.values()) {
//            System.out.println("| " + exceptionEnum.getCode() + " | " + exceptionEnum.getMessage() + " |");
//        }
//    }
}
